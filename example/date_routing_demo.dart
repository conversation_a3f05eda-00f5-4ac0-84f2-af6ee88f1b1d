/// 演示 KibanaService 日期路由逻辑的示例
/// 
/// 这个文件展示了如何根据结束时间来选择不同的 API 端点：
/// - 如果结束时间 < 2025.6.25 00:00:00，使用旧的 Kibana API (kibanalb.staff.xdf.cn)
/// - 如果结束时间 >= 2025.6.25 00:00:00，使用新的 API (n9elol.staff.xdf.cn)

void main() {
  print('=== Kibana Service 日期路由逻辑演示 ===\n');
  
  final cutoffDate = DateTime(2025, 6, 25); // 2025.6.25 00:00:00
  print('分界日期: ${cutoffDate.toString()}\n');
  
  // 测试各种日期场景
  final testDates = [
    DateTime(2025, 1, 1),           // 2025年初
    DateTime(2025, 6, 20),          // 6月20日
    DateTime(2025, 6, 24, 23, 59, 59), // 6月24日最后一秒
    DateTime(2025, 6, 25, 0, 0, 0),    // 6月25日零点 (特殊范围开始)
    DateTime(2025, 6, 25, 12, 0, 0),   // 6月25日中午
    DateTime(2025, 6, 26, 12, 0, 0),   // 6月26日中午
    DateTime(2025, 6, 26, 23, 59, 59), // 6月26日最后一秒 (特殊范围结束)
    DateTime(2025, 6, 27, 0, 0, 0),    // 6月27日零点
    DateTime(2025, 7, 1),           // 7月1日
    DateTime.now(),                 // 当前时间
  ];
  
  for (final date in testDates) {
    final useLegacy = shouldUseLegacyApi(date);
    final useSpecialIndex = shouldUseSpecialIndex(date);
    final apiType = useLegacy ? '旧 API (Legacy Kibana)' : '新 API (n9elol)';
    final endpoint = useLegacy
        ? 'kibanalb.staff.xdf.cn/s/weixinshengtai/elasticsearch/_msearch'
        : 'n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch';

    print('日期: ${date.toString()}');
    print('  -> 使用: $apiType');
    print('  -> 端点: $endpoint');
    print('  -> 索引: ${getIndexPattern(date, useLegacy, useSpecialIndex)}');
    if (useSpecialIndex) {
      print('  -> 特殊: 使用 p1-weixinshengtai 索引 (2025.6.25-2025.6.26 期间)');
    }
    print('');
  }
  
  print('=== 路由逻辑说明 ===');
  print('1. 旧 API (结束时间 < 2025.6.25 00:00:00):');
  print('   - 端点: https://kibanalb.staff.xdf.cn/s/weixinshengtai/elasticsearch/_msearch');
  print('   - 索引: p1-pro-work-wechat-magic-* (动态索引模式)');
  print('   - 特点: 使用传统的 Kibana Elasticsearch API');
  print('');
  print('2. 新 API - 特殊期间 (2025.6.25 00:00:00 - 2025.6.26 23:59:59):');
  print('   - 端点: https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch');
  print('   - 索引: p1-weixinshengtai (特殊索引)');
  print('   - 特点: 使用新 API 但特殊索引');
  print('');
  print('3. 新 API - 正常期间 (>= 2025.6.27 00:00:00):');
  print('   - 端点: https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch');
  print('   - 索引: p1-pro-work-wechat-magic (正常索引)');
  print('   - 特点: 使用新的代理 API，性能更好');
}

/// 判断是否应该使用旧的 Kibana API
bool shouldUseLegacyApi(DateTime endTime) {
  final cutoffDate = DateTime(2025, 6, 25); // 2025.6.25 00:00:00
  return endTime.isBefore(cutoffDate);
}

/// 判断是否应该使用特殊索引 (p1-weixinshengtai)
bool shouldUseSpecialIndex(DateTime endTime) {
  final cutoffDate = DateTime(2025, 6, 25); // 2025.6.25 00:00:00
  final specialEndDate = DateTime(2025, 6, 27); // 2025.6.27 00:00:00
  return !endTime.isBefore(cutoffDate) && endTime.isBefore(specialEndDate);
}

/// 根据日期和API类型获取索引模式
String getIndexPattern(DateTime date, bool useLegacy, bool useSpecialIndex) {
  if (useLegacy) {
    return 'p1-pro-work-wechat-magic-*';
  } else if (useSpecialIndex) {
    return 'p1-weixinshengtai';
  } else {
    return 'p1-pro-work-wechat-magic';
  }
}
