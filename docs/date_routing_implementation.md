# Kibana Service 日期路由实现

## 概述

根据您的要求，我已经成功实现了基于日期范围的 API 路由逻辑，包括：
1. 当选择的日期范围结束时间小于 2025.6.25 00:00:00 时，系统会自动使用旧的 Kibana API 方案
2. 当日期范围在 2025.6.25 00:00:00 - 2025.6.26 23:59:59 内时，使用新 API 但特殊索引 `p1-weixinshengtai`
3. 日期选择器已设置为中文语言，并移除了时间限制

## 实现详情

### 1. 路由逻辑

在 `lib/kibana_service.dart` 中的 `fetchLogs` 方法中添加了日期路由逻辑：

```dart
// --- 日期范围路由逻辑 ---
final cutoffDate = DateTime(2025, 6, 25); // 2025.6.25 00:00:00

// 如果结束时间小于 2025.6.25 00:00:00，使用旧的 Kibana 方案
if (endTime.isBefore(cutoffDate)) {
  return _fetchLogsLegacyKibana(...);
}

// 否则使用当前的新方案 (n9elol.staff.xdf.cn)
return _fetchLogsNewApi(...);
```

### 2. API 方案对比

#### 旧 API 方案 (`_fetchLogsLegacyKibana`)
- **适用时间**: 结束时间 < 2025.6.25 00:00:00
- **API 端点**: `https://kibanalb.staff.xdf.cn/s/weixinshengtai/elasticsearch/_msearch`
- **索引模式**: 使用传入的 `indexPattern` 参数 (如 `p1-pro-work-wechat-magic-*`)
- **特点**: 使用传统的 Kibana Elasticsearch API

#### 新 API 方案 - 特殊期间 (`_fetchLogsNewApi`)
- **适用时间**: 2025.6.25 00:00:00 - 2025.6.26 23:59:59
- **API 端点**: `https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch`
- **索引模式**: 固定使用 `p1-weixinshengtai`
- **特点**: 使用新 API 但特殊索引

#### 新 API 方案 - 正常期间 (`_fetchLogsNewApi`)
- **适用时间**: 结束时间 >= 2025.6.27 00:00:00
- **API 端点**: `https://n9elol.staff.xdf.cn/api/n9e/proxy/29/_msearch`
- **索引模式**: 使用传入的 `indexPattern` 参数 (通常为 `p1-pro-work-wechat-magic`)
- **特点**: 使用新的代理 API，性能更好

### 3. 关键差异

| 特性 | 旧 API | 新 API |
|------|--------|--------|
| 基础 URL | kibanalb.staff.xdf.cn | n9elol.staff.xdf.cn |
| 路径 | /s/weixinshengtai/elasticsearch/_msearch | /api/n9e/proxy/29/_msearch |
| 索引处理 | 动态索引模式 | 固定索引名称 |
| 索引筛选 | `p1-pro-work-wechat-magic-*` 时添加 "report task log" 筛选 | `p1-pro-work-wechat-magic` 时添加 "report task log" 筛选 |

### 4. 测试验证

创建了完整的测试套件来验证日期路由逻辑：

- **边界测试**: 验证 2025.6.25 00:00:00 这个精确分界点
- **日期范围测试**: 测试各种日期场景
- **API 选择测试**: 确保正确的 API 被选择

运行测试：
```bash
dart test test/kibana_service_date_routing_test.dart
```

### 5. 演示程序

创建了演示程序 `example/date_routing_demo.dart` 来展示路由逻辑：

```bash
dart run example/date_routing_demo.dart
```

## 使用方式

使用方式与之前完全相同，系统会根据 `endTime` 参数自动选择合适的 API：

```dart
final kibanaService = KibanaService();

// 查询 2025年6月20日的数据 -> 自动使用旧 API
await kibanaService.fetchLogs(
  startTime: DateTime(2025, 6, 20),
  endTime: DateTime(2025, 6, 20, 23, 59, 59),
  indexPattern: 'p1-pro-work-wechat-magic-*',
);

// 查询 2025年6月26日的数据 -> 自动使用新 API
await kibanaService.fetchLogs(
  startTime: DateTime(2025, 6, 26),
  endTime: DateTime(2025, 6, 26, 23, 59, 59),
  indexPattern: 'p1-pro-work-wechat-magic',
);
```

## 兼容性

- ✅ 向后兼容：现有代码无需修改
- ✅ 自动路由：根据日期自动选择 API
- ✅ 错误处理：两套 API 都有完整的错误处理
- ✅ 日志记录：详细的调试日志帮助排查问题

## 日期选择器改进

### 中文本地化
- 添加了 `flutter_localizations` 依赖
- 在 `app.dart` 中配置了中文本地化支持
- 日期选择器界面显示为中文
- 时间显示格式改为中文 ("开始" 和 "结束")

### 移除时间限制
- 移除了开始时间和结束时间的相互限制
- 用户可以自由选择任意时间范围
- 开始时间不再限制结束时间的最小值
- 结束时间不再限制开始时间的最大值

## 注意事项

1. **日期边界**:
   - 2025.6.25 00:00:00 是旧 API 和新 API 的分界点
   - 2025.6.27 00:00:00 是特殊索引和正常索引的分界点
2. **索引模式**:
   - 旧 API 支持通配符索引 (`p1-pro-work-wechat-magic-*`)
   - 特殊期间使用固定索引 (`p1-weixinshengtai`)
   - 正常期间使用传入的索引模式
3. **筛选条件**: 所有 API 都会根据索引模式添加相应的筛选条件
4. **性能**: 新 API 通常性能更好，建议在可能的情况下使用

## 文件结构

```
lib/
├── kibana_service.dart          # 主要实现文件
├── models/
│   ├── query_ast.dart          # 查询 AST 模型
│   └── filter.dart             # 筛选器模型
test/
└── kibana_service_date_routing_test.dart  # 测试文件
example/
└── date_routing_demo.dart      # 演示程序
docs/
└── date_routing_implementation.md  # 本文档
```

这个实现确保了系统能够根据查询的时间范围自动选择最合适的 API 端点，同时保持了代码的简洁性和可维护性。
