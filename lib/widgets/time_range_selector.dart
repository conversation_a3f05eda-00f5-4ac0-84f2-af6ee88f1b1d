import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:omni_datetime_picker/omni_datetime_picker.dart';

class TimeRangeSelector extends StatelessWidget {
  const TimeRangeSelector({
    super.key,
    required this.startTime,
    required this.endTime,
    required this.onTimeRangeSelected,
  });

  final DateTime startTime;
  final DateTime endTime;
  final Function(DateTime, DateTime) onTimeRangeSelected;

  String _formatDateTime(DateTime dt) {
    return DateFormat('yy-MM-dd HH:mm:ss').format(dt);
  }

  Future<void> _showOmniPicker(BuildContext context) async {
    final List<DateTime>? dateTimeList = await showOmniDateTimeRangePicker(
      context: context,
      startInitialDate: startTime,
      startFirstDate: DateTime(2020), // 移除开始时间限制
      startLastDate: DateTime(2030),  // 移除开始时间限制
      endInitialDate: endTime,
      endFirstDate: DateTime(2020),   // 移除结束时间限制
      endLastDate: DateTime(2030),    // 移除结束时间限制
      is24HourMode: true,
      isShowSeconds: true,
      minutesInterval: 1,
      secondsInterval: 1,
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      constraints: const BoxConstraints(
        maxWidth: 350,
        maxHeight: 650,
      ),
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1.drive(
            Tween(
              begin: 0,
              end: 1,
            ),
          ),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      barrierDismissible: true,
      // 添加中文本地化设置
      startDateTimePickerOptions: const OmniDateTimePickerOptions(
        locale: Locale('zh', 'CN'),
      ),
      endDateTimePickerOptions: const OmniDateTimePickerOptions(
        locale: Locale('zh', 'CN'),
      ),
    );

    if (dateTimeList != null && dateTimeList.length == 2) {
      onTimeRangeSelected(dateTimeList[0], dateTimeList[1]);
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _showOmniPicker(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade400),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.calendar_today, size: 18.0, color: Colors.blueAccent),
            const SizedBox(width: 8.0),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '开始: ${_formatDateTime(startTime)}',
                  style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12, color: Colors.black87),
                ),
                Text(
                  '结束: ${_formatDateTime(endTime)}',
                  style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 12, color: Colors.black87),
                ),
              ],
            ),
            const SizedBox(width: 8.0),
            const Icon(Icons.arrow_drop_down, color: Colors.black54),
          ],
        ),
      ),
    );
  }
} 